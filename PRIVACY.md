# Votum Privacy Policy

## Data that is stored

When you use Votum, the following information is stored on our servers:

- Council configuration, which includes all configuration points listed at <https://github.com/evaera/Votum#configuration-points>
- List of all present and past motions created by council members, including
  - Motion text
  - Motion author name at the time of creation
  - Timestamp
  - Motion status
  - All votes, including the name of the voters, how they voted, and the reasons they provided for the vote

This data is exportable by the user by running the `!archive export` command.

## Data deletion, questions, and concerns

You can request your data to be deleted by emailing votum *A@T* eryn *D.O.T* io and including the channel ID that your council is in. For questions and concerns, feel free to open an issue or discussion on the GitHub repository located at https://github.com/evaera/Votum/new/master or feel free to email the same address.
