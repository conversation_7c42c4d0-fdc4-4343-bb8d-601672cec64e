import Discord from "discord.js"
import { TextChannel, Message } from "discord.js"
import { CommandoClient, CommandoMessage } from "discord.js-commando"
import Command from "../Command"

type Args = {
  reason: string
  candDur: number
  voteDur: number
}

export default class ElectionsCommand extends Command {
  constructor(client: CommandoClient) {
    super(client, {
      name: "elections",
      description: "Lance une élection",
      args: [
        {
          key: "reason",
          prompt: "Quel est le motif de l'élection ?",
          type: "string",
          default: ""
        },
        {
          key: "candDur",
          prompt: "Durée phase de candidature (minutes) ?",
          type: "integer",
          default: 0
        },
        {
          key: "voteDur",
          prompt: "Durée phase de vote (minutes) ?",
          type: "integer",
          default: 0
        }
      ],
      adminOnly: true
    })
  }

  async run(
    msg: CommandoMessage,
    args: Args
  ): Promise<Message | Message[]> {
    const democratie = (this.client as any).democratie
    const pollManager = democratie.pollManager

    // Check if this is a subcommand (new syntax)
    const subcommands = ["list", "status", "kill"]
    if (subcommands.includes(args.reason.toLowerCase())) {
      return this.handleSubcommand(msg, pollManager, args.reason.toLowerCase(), args.candDur, args.voteDur)
    }

    // Handle legacy syntax
    return this.handleLegacySyntax(msg, pollManager, args)
  }

  private async handleSubcommand(
    msg: CommandoMessage, 
    pollManager: any, 
    subcommand: string, 
    arg2: number, 
    arg3: number
  ): Promise<Message | Message[]> {
    switch (subcommand) {
      case "list":
        return this.handleList(msg, pollManager)
      case "status":
        return this.handleStatus(msg, pollManager, "")
      case "kill":
        return this.handleKill(msg, pollManager, "")
      default:
        return msg.reply("❌ Sous-commande invalide. Utilisez: list, status, kill")
    }
  }

  private async handleLegacySyntax(
    msg: CommandoMessage,
    pollManager: any,
    args: Args
  ): Promise<Message | Message[]> {
    // !elections kill (legacy)
    if (args.reason.toLowerCase() === "kill") {
      const deducedId = pollManager.deduceElectionId()
      if (!deducedId) {
        return msg.reply("⚠️ Aucune élection active à interrompre.")
      }
      const success = pollManager.killElection(deducedId)
      if (success) {
        return msg.say("🛑 Élection annulée manuellement.")
      } else {
        return msg.reply("❌ Impossible d'annuler cette élection.")
      }
    }

    // !elections without args (legacy - show status)
    const noArgs =
      args.reason.trim() === "" &&
      args.candDur === 0 &&
      args.voteDur === 0

    if (noArgs) {
      return this.handleStatus(msg, pollManager, "")
    }

    // Legacy create syntax: !elections "raison" candDur voteDur
    if (args.reason.trim() !== "" && args.candDur > 0 && args.voteDur > 0) {
      return this.handleLegacyCreate(msg, pollManager, args)
    }

    // Invalid usage
    return msg.reply("❌ Usage incorrect. Exemple : `!elections \"Délégué de classe\" 10 20`")
  }

  private async handleLegacyCreate(
    msg: CommandoMessage,
    pollManager: any,
    args: Args
  ): Promise<Message | Message[]> {
    const msCand = args.candDur * 60_000
    const msVote = args.voteDur * 60_000

    const { id, election } = pollManager.createElection(args.reason, msCand, msVote, msg.channel as TextChannel)
    const shortId = id.substring(0, 8)

    await msg.say(
      `📢 Élection créée (ID: ${shortId}): **${args.reason}**\n` +
      `Candidatures jusqu'à <t:${Math.floor(election.data.endsCandidacyAt / 1000)}:R>`
    )

    await election.startCandidacy(msCand)
    return msg.reply(`🗳️ Phase de candidature lancée pour l'élection ${shortId}.`)
  }

  private async handleList(msg: CommandoMessage, pollManager: any): Promise<Message | Message[]> {
    const elections = pollManager.getActiveElections()
    
    if (elections.length === 0) {
      return msg.reply("ℹ️ Aucune élection en cours.")
    }

    const embed = new Discord.MessageEmbed()
      .setColor("BLUE")
      .setTitle("📊 Élections en cours")

    for (const { id, election } of elections) {
      const shortId = id.substring(0, 8)
      let status = ""
      
      if (election.data.phase === "Candidacy") {
        status = `Phase de candidature - ${election.data.candidates.length} candidat(s)`
      } else if (election.data.phase === "Voting") {
        const totalVotes = election.data.votes.length
        status = `Phase de vote - ${totalVotes} vote(s)`
      }

      embed.addField(
        `${shortId} - ${election.data.reason}`,
        status,
        false
      )
    }

    return msg.embed(embed)
  }

  private async handleStatus(msg: CommandoMessage, pollManager: any, targetId: string): Promise<Message | Message[]> {
    // If no ID provided, try to deduce from single active election
    if (!targetId) {
      const deducedId = pollManager.deduceElectionId()
      if (!deducedId) {
        const elections = pollManager.getActiveElections()
        if (elections.length === 0) {
          return msg.reply("ℹ️ Il n'y a actuellement **aucune** élection en cours.")
        }
        return msg.reply("❌ Plusieurs élections actives. Spécifiez un ID.")
      }
      targetId = deducedId
    }

    // Find election by partial ID match
    const elections = pollManager.getActiveElections()
    const election = elections.find(({ id }: any) => id.startsWith(targetId))?.election

    if (!election) {
      return msg.reply("❌ Élection introuvable avec cet ID.")
    }

    const embed = new Discord.MessageEmbed()
      .setColor("ORANGE")
      .setTitle("📊 État de l'élection")
      .setDescription(`**Objet :** ${election.data.reason}`)

    if (election.data.phase === "Candidacy") {
      embed.setFooter(
        `Fin des candidatures <t:${Math.floor(election.data.endsCandidacyAt / 1000)}:R>`
      )
      embed.addField(
        "Candidats",
        election.data.candidates.length > 0
          ? election.data.candidates.map((c: any) => `• ${c.name}`).join("\n")
          : "Aucun candidat pour le moment",
        false
      )
    } else if (election.data.phase === "Voting") {
      embed.setFooter(
        `Fin du vote <t:${Math.floor(election.data.endsVotingAt! / 1000)}:R>`
      )
      election.data.candidates.forEach((c: any, i: number) => {
        const votesFor = election.data.votes.filter((v: any) => v.candidateId === c.id).length
        embed.addField(`${election.emojis[i]} ${c.name}`, `${votesFor} vote(s)`, true)
      })
    } else {
      embed.setFooter("Élection terminée")
      const resultLines = election.data.candidates
        .map((c: any) => {
          const count = election.data.votes.filter((v: any) => v.candidateId === c.id).length
          return `• ${c.name} : ${count} vote(s)`
        })
        .join("\n")
      embed.addField("Résultats", resultLines || "Aucun vote enregistré.")
    }

    return msg.embed(embed)
  }

  private async handleKill(msg: CommandoMessage, pollManager: any, targetId: string): Promise<Message | Message[]> {
    // If no ID provided, try to deduce from single active election
    if (!targetId) {
      const deducedId = pollManager.deduceElectionId()
      if (!deducedId) {
        return msg.reply("❌ Plusieurs élections actives. Spécifiez un ID.")
      }
      targetId = deducedId
    }

    // Find election by partial ID match
    const elections = pollManager.getActiveElections()
    const matchingElection = elections.find(({ id }: any) => id.startsWith(targetId))

    if (!matchingElection) {
      return msg.reply("❌ Élection introuvable avec cet ID.")
    }

    const success = pollManager.killElection(matchingElection.id)
    if (success) {
      const shortId = matchingElection.id.substring(0, 8)
      return msg.say(`🛑 Élection ${shortId} annulée manuellement.`)
    } else {
      return msg.reply("❌ Impossible d'annuler cette élection.")
    }
  }
}
