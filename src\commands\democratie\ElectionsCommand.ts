import Discord from "discord.js"
import { TextChannel, Message } from "discord.js"
import { CommandoClient, CommandoMessage } from "discord.js-commando"
import Command from "../Command"
import Election from "../../Election"
import { ElectionData } from "../../ElectionData"

type Args = {
  reason: string
  candDur: number
  voteDur: number
}

export default class ElectionsCommand extends Command {
  constructor(client: CommandoClient) {
    super(client, {
      name: "elections",
      description: "Lance une élection ou affiche l’état actuel",
      args: [
        {
          key: "reason",
          prompt: "Quel est le motif de l’élection ?",
          type: "string",
          default: ""
        },
        {
          key: "candDur",
          prompt: "Durée phase de candidature (minutes) ?",
          type: "integer",
          default: 0
        },
        {
          key: "voteDur",
          prompt: "Durée phase de vote (minutes) ?",
          type: "integer",
          default: 0
        }
      ],
      adminOnly: true
    })
  }

  async run(
    msg: CommandoMessage,
    args: Args
  ): Promise<Message | Message[]> {
    const democratie = (this.client as any).democratie
    const pollManager = democratie.pollManager

    // Check if this is a subcommand (new syntax)
    const subcommands = ["list", "create", "status", "kill"]
    if (subcommands.includes(args.reason.toLowerCase())) {
      return this.handleSubcommand(msg, pollManager, args.reason.toLowerCase(), args.candDur, args.voteDur)
    }

    // Handle legacy syntax
    return this.handleLegacySyntax(msg, pollManager, args)
  }

  private async handleSubcommand(
    msg: CommandoMessage,
    pollManager: any,
    subcommand: string,
    arg2: number,
    arg3: number
  ): Promise<Message | Message[]> {
    switch (subcommand) {
      case "list":
        return this.handleList(msg, pollManager)
      case "create":
        // For create, we need to parse the arguments differently
        return msg.reply("❌ Usage: !elections create \"raison\" <candDur> <voteDur>")
      case "status":
        return this.handleStatus(msg, pollManager, "")
      case "kill":
        return this.handleKill(msg, pollManager, "")
      default:
        return msg.reply("❌ Sous-commande invalide. Utilisez: list, create, status, kill")
    }
  }

  private async handleLegacySyntax(
    msg: CommandoMessage,
    pollManager: any,
    args: Args
  ): Promise<Message | Message[]> {
    // !elections kill (legacy)
    if (args.reason.toLowerCase() === "kill") {
      const deducedId = pollManager.deduceElectionId()
      if (!deducedId) {
        return msg.reply("⚠️ Aucune élection active à interrompre.")
      }
      const success = pollManager.killElection(deducedId)
      if (success) {
        return msg.say("🛑 Élection annulée manuellement.")
      } else {
        return msg.reply("❌ Impossible d'annuler cette élection.")
      }
    }

    // !elections without args
    const noArgs =
      args.reason.trim() === "" &&
      args.candDur === 0 &&
      args.voteDur === 0

    if (noArgs) {
      if (!active) {
        return msg.reply("ℹ️ Il n’y a actuellement **aucune** élection en cours.")
      }
      // stats display
      const embed = new Discord.MessageEmbed()
        .setColor("ORANGE")
        .setTitle("📊 État actuel des élections")
        .setDescription(`**Objet :** ${active.data.reason}`)

      if (active.data.phase === "Candidacy") {
        embed.setFooter(
          `Fin des candidatures <t:${Math.floor(
            active.data.endsCandidacyAt / 1000
          )}:R>`
        )
        embed.addField(
          "Candidats",
          active.data.candidates.length > 0
            ? active.data.candidates.map((c) => `• ${c.name}`).join("\n")
            : "Aucun candidat pour le moment",
          false
        )
      } else if (active.data.phase === "Voting") {
        embed.setFooter(
          `Fin du vote <t:${Math.floor(
            active.data.endsVotingAt! / 1000
          )}:R>`
        )
        active.data.candidates.forEach((c, i) => {
          const votesFor = active.data.votes.filter(
            (v) => v.candidateId === c.id
          ).length
          embed.addField(
            `${active.emojis[i]} ${c.name}`,
            `${votesFor} vote(s)`,
            true
          )
        })
      } else {
        embed.setFooter("Élection terminée")
        const resultLines = active.data.candidates
          .map((c) => {
            const count = active.data.votes.filter(
              (v) => v.candidateId === c.id
            ).length
            return `• ${c.name} : ${count} vote(s)`
          })
          .join("\n")
        embed.addField("Résultats", resultLines || "Aucun vote enregistré.")
      }

      return msg.embed(embed)
    }

    // incorrect settings
    if (
      args.reason.trim() === "" ||
      args.candDur <= 0 ||
      args.voteDur <= 0
    ) {
      return msg.reply(
        "❌ Usage incorrect. Exemple : `!elections \"Délégué de classe\" 10 20`"
      )
    }

    // can't launch election if one is already ative
    if (active && active.data.phase !== "Finished") {
      return msg.reply(
        "⚠️ Une élection est déjà en cours. Tapez `!elections kill` pour l’interrompre."
      )
    }

    // create new election
    const msCand = args.candDur * 60_000
    const msVote = args.voteDur * 60_000
    const endsCandidacyAt = Date.now() + msCand

    const data: ElectionData = {
      reason: args.reason,
      phase: "Candidacy",
      endsCandidacyAt,
      candidates: [],
      votes: []
    }

    const newElection = new Election(data, msg.channel as TextChannel)
    democratie.currentElection = newElection

    // creation message
    await msg.say(
      `📢 Élections ouvertes : **${args.reason}**\n` +
        `Candidatures jusqu’à <t:${Math.floor(
          endsCandidacyAt / 1000
        )}:R>`
    )
    await newElection.startCandidacy(msCand)

    // time left for candidates to apply
    setTimeout(async () => {
      // no candidates = end elections immediatly
      if (newElection.data.candidates.length === 0) {
        await msg.say(
          "⚠️ Aucune candidature reçue. Résultats immédiats."
        )
        await newElection.announceResults()
        democratie.currentElection = undefined
        return
      }

      // else vote phase starts
      await newElection.startVoting(msVote)

      // time left to vote
      setTimeout(() => {
        newElection.announceResults().catch(console.error)
        democratie.currentElection = undefined
      }, msVote)
    }, msCand)

    return msg.reply("🗳️ Phase de candidature lancée.")
  }
}