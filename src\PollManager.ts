import { TextChannel } from "discord.js"
import { v4 as uuidv4 } from "uuid"
import Election from "./Election"
import { ElectionData } from "./ElectionData"
import Motion, { MotionResolution } from "./Motion"
import Council from "./Council"

export interface PollInfo {
  id: string
  type: "election" | "motion"
  title: string
  phase?: string
  createdAt: number
}

export default class PollManager {
  private elections: Map<string, Election> = new Map()
  private motions: Map<string, Motion> = new Map()
  private electionTimers: Map<string, NodeJS.Timeout[]> = new Map()
  private motionTimers: Map<string, NodeJS.Timeout[]> = new Map()

  constructor() {}

  // ─── ELECTIONS ─────────────────────────────────────────────────────────────

  public createElection(
    reason: string,
    candDurMs: number,
    voteDurMs: number,
    channel: TextChannel
  ): { id: string; election: Election } {
    const id = uuidv4()
    const endsCandidacyAt = Date.now() + candDurMs

    const data: ElectionData = {
      reason,
      phase: "Candidacy",
      endsCandidacyAt,
      candidates: [],
      votes: []
    }

    const election = new Election(data, channel)
    this.elections.set(id, election)

    // Set up timers
    const timers: NodeJS.Timeout[] = []
    
    // Timer for candidacy phase
    const candidacyTimer = setTimeout(async () => {
      if (election.data.candidates.length === 0) {
        // No candidates, end immediately
        await election.announceResults()
        this.removeElection(id)
        return
      }

      // Start voting phase
      await election.startVoting(voteDurMs)
      
      // Timer for voting phase
      const votingTimer = setTimeout(async () => {
        await election.announceResults()
        this.removeElection(id)
      }, voteDurMs)
      
      timers.push(votingTimer)
    }, candDurMs)
    
    timers.push(candidacyTimer)
    this.electionTimers.set(id, timers)

    return { id, election }
  }

  public getElection(id: string): Election | undefined {
    return this.elections.get(id)
  }

  public getElections(): Map<string, Election> {
    return this.elections
  }

  public getActiveElections(): { id: string; election: Election }[] {
    const active: { id: string; election: Election }[] = []
    for (const [id, election] of this.elections.entries()) {
      if (election.data.phase !== "Finished") {
        active.push({ id, election })
      }
    }
    return active
  }

  public killElection(id: string): boolean {
    const election = this.elections.get(id)
    if (!election) return false

    election.data.phase = "Finished"
    this.removeElection(id)
    return true
  }

  private removeElection(id: string): void {
    // Clear timers
    const timers = this.electionTimers.get(id)
    if (timers) {
      timers.forEach(timer => clearTimeout(timer))
      this.electionTimers.delete(id)
    }
    
    this.elections.delete(id)
  }

  // ─── MOTIONS ───────────────────────────────────────────────────────────────

  public createMotion(council: Council, motionData: any): { id: string; motion: Motion } {
    const id = uuidv4()
    const motion = council.createMotion(motionData)
    this.motions.set(id, motion)
    
    // Store empty timers array for potential future use
    this.motionTimers.set(id, [])
    
    return { id, motion }
  }

  public getMotion(id: string): Motion | undefined {
    return this.motions.get(id)
  }

  public getMotions(): Map<string, Motion> {
    return this.motions
  }

  public getActiveMotions(): { id: string; motion: Motion }[] {
    const active: { id: string; motion: Motion }[] = []
    for (const [id, motion] of this.motions.entries()) {
      if (motion.getData().active) {
        active.push({ id, motion })
      }
    }
    return active
  }

  public killMotion(id: string): boolean {
    const motion = this.motions.get(id)
    if (!motion) return false

    motion.resolve(MotionResolution.Killed)
    this.removeMotion(id)
    return true
  }

  private removeMotion(id: string): void {
    // Clear timers if any
    const timers = this.motionTimers.get(id)
    if (timers) {
      timers.forEach(timer => clearTimeout(timer))
      this.motionTimers.delete(id)
    }
    
    this.motions.delete(id)
  }

  // ─── GENERAL UTILITIES ─────────────────────────────────────────────────────

  public getAllPolls(): PollInfo[] {
    const polls: PollInfo[] = []
    
    // Add elections
    for (const [id, election] of this.elections.entries()) {
      polls.push({
        id,
        type: "election",
        title: election.data.reason,
        phase: election.data.phase,
        createdAt: election.data.endsCandidacyAt - (election.data.endsCandidacyAt - Date.now()) // Approximate creation time
      })
    }
    
    // Add motions
    for (const [id, motion] of this.motions.entries()) {
      polls.push({
        id,
        type: "motion",
        title: motion.text,
        createdAt: motion.createdAt
      })
    }
    
    return polls.sort((a, b) => b.createdAt - a.createdAt)
  }

  public getElectionByMessageId(messageId: string): { id: string; election: Election } | undefined {
    for (const [id, election] of this.elections.entries()) {
      if (election.data.messageId === messageId) {
        return { id, election }
      }
    }
    return undefined
  }

  public getMotionByMessageId(messageId: string): { id: string; motion: Motion } | undefined {
    for (const [id, motion] of this.motions.entries()) {
      if (motion.messageId === messageId) {
        return { id, motion }
      }
    }
    return undefined
  }

  // Auto-deduce ID if only one active poll of the given type
  public deduceElectionId(): string | undefined {
    const active = this.getActiveElections()
    return active.length === 1 ? active[0].id : undefined
  }

  public deduceMotionId(): string | undefined {
    const active = this.getActiveMotions()
    return active.length === 1 ? active[0].id : undefined
  }
}
