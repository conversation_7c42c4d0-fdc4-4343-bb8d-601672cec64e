import { Message } from "discord.js";
import { CommandoClient, CommandoMessage } from "discord.js-commando";
import Command from "../Command";
import { CastVoteStatus } from "../../Motion";

type Args = {
  vote: string;
  reason: string;
  pollId: string;
}

export default class VoteCommand extends Command {
  constructor(client: CommandoClient) {
    super(client, {
      name: "vote",
      description: "Voter pour une élection ou motion",
      args: [
        {
          key: "vote",
          prompt: "Votre vote (pour/contre/blanc)",
          type: "string",
        },
        {
          key: "reason",
          prompt: "Raison de votre vote (optionnel)",
          type: "string",
          default: ""
        },
        {
          key: "pollId",
          prompt: "ID de l'élection/motion (optionnel)",
          type: "string",
          default: ""
        }
      ],
    });
  }

  async run(
    msg: CommandoMessage,
    args: Args
  ): Promise<Message | Message[]> {
    const { vote, reason, pollId } = args;
    const democratie = (this.client as any).democratie;
    const pollManager = democratie.pollManager;

    const voteType = this.parseVoteType(vote.toLowerCase());
    if (voteType === null) {
      return msg.reply("❌ Vote invalide. Utilisez: pour, contre, ou blanc");
    }

    let targetId = pollId.trim();
    
    // If no ID provided, try to find a single active poll
    if (!targetId) {
      const activeElections = pollManager.getActiveElections();
      const activeMotions = pollManager.getActiveMotions();
      
      if (activeElections.length + activeMotions.length === 0) {
        return msg.reply("❌ Aucune élection ou motion active.");
      }
      
      if (activeElections.length + activeMotions.length > 1) {
        return msg.reply("❌ Plusieurs polls actifs. Spécifiez un ID avec: !vote <pour/contre/blanc> [raison] <pollId>");
      }
      
      // Single active poll found
      if (activeElections.length === 1) {
        return this.voteForElection(msg, activeElections[0].election, voteType);
      } else {
        return this.voteForMotion(msg, activeMotions[0].motion, voteType, reason);
      }
    }

    // Try to find election first
    const elections = pollManager.getActiveElections();
    const election = elections.find(({ id }: any) => id.startsWith(targetId))?.election;
    
    if (election) {
      return this.voteForElection(msg, election, voteType);
    }

    // Try to find motion
    const motions = pollManager.getActiveMotions();
    const motion = motions.find(({ id }: any) => id.startsWith(targetId))?.motion;
    
    if (motion) {
      return this.voteForMotion(msg, motion, voteType, reason);
    }

    return msg.reply("❌ Poll introuvable avec cet ID.");
  }

  private parseVoteType(vote: string): 1 | -1 | 0 | null {
    switch (vote) {
      case "pour":
      case "yes":
      case "oui":
      case "1":
        return 1;
      case "contre":
      case "no":
      case "non":
      case "-1":
        return -1;
      case "blanc":
      case "abstention":
      case "abs":
      case "0":
        return 0;
      default:
        return null;
    }
  }

  private async voteForElection(msg: CommandoMessage, election: any, voteType: 1 | -1 | 0): Promise<Message | Message[]> {
    if (election.data.phase !== "Voting") {
      return msg.reply("❌ Cette élection n'est pas en phase de vote.");
    }

    // For elections, we need to handle voting differently
    // Elections use emoji reactions, not direct vote casting
    return msg.reply("❌ Pour voter dans une élection, utilisez les réactions sur le message de vote.");
  }

  private async voteForMotion(msg: CommandoMessage, motion: any, voteType: 1 | -1 | 0, reason: string): Promise<Message | Message[]> {
    if (!motion.getData().active) {
      return msg.reply("❌ Cette motion n'est pas active.");
    }

    const council = this.getCouncil(msg.channel.id);
    const voteData = {
      authorId: msg.author.id,
      authorName: msg.member!.displayName,
      name: this.getVoteName(voteType),
      state: voteType,
      reason: reason || "",
      isDictator: council.getConfig("dictatorRole")
        ? msg.member!.roles.cache.has(council.getConfig("dictatorRole")!)
        : false,
    };

    const status = motion.castVote(voteData);

    switch (status) {
      case CastVoteStatus.New:
        await motion.postMessage();
        return msg.reply(`✅ Vote enregistré: ${this.getVoteName(voteType)}`);
      case CastVoteStatus.Changed:
        await motion.postMessage(`<@${msg.author.id}> a changé son vote en ${this.getVoteName(voteType)}.`);
        return msg.reply(`✅ Vote modifié: ${this.getVoteName(voteType)}`);
      default:
        return msg.reply("❌ Erreur lors de l'enregistrement du vote.");
    }
  }

  private getVoteName(voteType: 1 | -1 | 0): string {
    switch (voteType) {
      case 1:
        return "Pour";
      case -1:
        return "Contre";
      case 0:
        return "Blanc";
      default:
        return "Inconnu";
    }
  }

  private getCouncil(channelId: string) {
    const democratie = (this.client as any).democratie;
    return democratie.getCouncil(channelId);
  }
}
