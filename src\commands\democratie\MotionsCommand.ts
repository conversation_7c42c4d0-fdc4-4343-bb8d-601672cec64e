import Discord from "discord.js"
import { Message } from "discord.js"
import { CommandoClient, CommandoMessage } from "discord.js-commando"
import Command from "../Command"
import { MotionResolution } from "../../Motion"

type Args = {
  subcommand: string
  args: string
}

export default class MotionsCommand extends Command {
  constructor(client: CommandoClient) {
    super(client, {
      name: "motions",
      description: "Gère les motions avec sous-commandes: list, create, status, kill",
      args: [
        {
          key: "subcommand",
          prompt: "Sous-commande (list, create, status, kill)",
          type: "string",
          default: "list"
        },
        {
          key: "args",
          prompt: "Arguments pour la sous-commande",
          type: "string",
          default: ""
        }
      ],
      adminOnly: true
    })
  }

  async run(
    msg: CommandoMessage,
    args: Args
  ): Promise<Message | Message[]> {
    const democratie = (this.client as any).democratie
    const pollManager = democratie.pollManager

    const subcommand = args.subcommand.toLowerCase()
    const subArgs = args.args.trim()

    switch (subcommand) {
      case "list":
        return this.handleList(msg, pollManager)
      
      case "create":
        return this.handleCreate(msg, pollManager, subArgs)
      
      case "status":
        return this.handleStatus(msg, pollManager, subArgs)
      
      case "kill":
        return this.handleKill(msg, pollManager, subArgs)
      
      default:
        return msg.reply("❌ Sous-commande invalide. Utilisez: list, create, status, kill")
    }
  }

  private async handleList(msg: CommandoMessage, pollManager: any): Promise<Message | Message[]> {
    const motions = pollManager.getActiveMotions()
    
    if (motions.length === 0) {
      return msg.reply("ℹ️ Aucune motion en cours.")
    }

    const embed = new Discord.MessageEmbed()
      .setColor("GREEN")
      .setTitle("📋 Motions en cours")

    for (const { id, motion } of motions) {
      const shortId = id.substring(0, 8)
      const votes = motion.getVotes()
      const status = `Pour: ${votes.yes} | Contre: ${votes.no} | Blanc: ${votes.abs}`

      embed.addField(
        `${shortId} - ${motion.text.substring(0, 50)}${motion.text.length > 50 ? '...' : ''}`,
        status,
        false
      )
    }

    return msg.embed(embed)
  }

  private async handleCreate(msg: CommandoMessage, pollManager: any, subArgs: string): Promise<Message | Message[]> {
    // Parse arguments: "texte" [voteDur]
    const parts = subArgs.match(/^"([^"]+)"(?:\s+(\d+))?$/) || subArgs.match(/^(\S+)(?:\s+(\d+))?$/)
    
    if (!parts || !parts[1]) {
      return msg.reply("❌ Usage: !motions create \"texte de la motion\" [voteDur]")
    }

    const [, text] = parts
    // Note: voteDur could be implemented later for timed motions

    if (text.length > 2000) {
      return msg.reply("❌ Le texte de la motion est trop long (max 2000 caractères).")
    }

    // Create motion data similar to MotionCommand
    const motionData = {
      text,
      authorId: msg.author.id,
      authorName: msg.member!.displayName,
      createdAt: Date.now(),
      voteType: 0, // LegacyMotionVoteType.Majority
      active: true,
      resolution: MotionResolution.Unresolved,
      didExpire: false,
      votes: [],
      options: {}
    }

    const { id, motion } = pollManager.createMotion(this.council, motionData)
    const shortId = id.substring(0, 8)

    await motion.postMessage(true)
    return msg.reply(`📋 Motion créée (ID: ${shortId}).`)
  }

  private async handleStatus(msg: CommandoMessage, pollManager: any, subArgs: string): Promise<Message | Message[]> {
    let targetId = subArgs.trim()
    
    // If no ID provided, try to deduce from single active motion
    if (!targetId) {
      const deducedId = pollManager.deduceMotionId()
      if (!deducedId) {
        return msg.reply("❌ Plusieurs motions actives. Spécifiez un ID.")
      }
      targetId = deducedId
    }

    // Find motion by partial ID match
    const motions = pollManager.getActiveMotions()
    const motion = motions.find(({ id }: any) => id.startsWith(targetId))?.motion

    if (!motion) {
      return msg.reply("❌ Motion introuvable avec cet ID.")
    }

    return motion.postMessage()
  }

  private async handleKill(msg: CommandoMessage, pollManager: any, subArgs: string): Promise<Message | Message[]> {
    let targetId = subArgs.trim()
    
    // If no ID provided, try to deduce from single active motion
    if (!targetId) {
      const deducedId = pollManager.deduceMotionId()
      if (!deducedId) {
        return msg.reply("❌ Plusieurs motions actives. Spécifiez un ID.")
      }
      targetId = deducedId
    }

    // Find motion by partial ID match
    const motions = pollManager.getActiveMotions()
    const matchingMotion = motions.find(({ id }: any) => id.startsWith(targetId))

    if (!matchingMotion) {
      return msg.reply("❌ Motion introuvable avec cet ID.")
    }

    // Check permissions (similar to original MotionCommand)
    const motion = matchingMotion.motion
    if (
      motion.authorId !== msg.author.id &&
      !msg.member!.hasPermission("MANAGE_GUILD") &&
      !msg.member!.roles.cache.find((role) => role.name === "Democratie Admin")
    ) {
      return msg.reply("❌ Tu n'as pas la permission de tuer cette motion.")
    }

    const success = pollManager.killMotion(matchingMotion.id)
    if (success) {
      const shortId = matchingMotion.id.substring(0, 8)
      await motion.postMessage()
      return msg.say(`🛑 Motion ${shortId} annulée manuellement.`)
    } else {
      return msg.reply("❌ Impossible d'annuler cette motion.")
    }
  }
}
