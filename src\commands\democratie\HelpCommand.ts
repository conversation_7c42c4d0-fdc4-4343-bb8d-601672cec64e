import { MessageEmbed } from 'discord.js';
import { CommandoClient, CommandoMessage } from 'discord.js-commando';
import Command from '../Command';

const SYNTAXES: Record<string, string> = {
  elections: "!elections [\"raison\" candDur voteDur] | [list|status|kill]",
  candidat: "!candidat \"raison\" [electionId]",
  motion: "!motion [\"texte\"] | [list|status|kill] [id]",
  vote: "!vote <pour|contre|blanc> [raison] [pollId]",
  yes: "!yes [raison]",
  no: "!no [raison]",
  abstain: "!abstain [raison]",
  pinginactive: "!pinginactive",
  archive:"!archive [plage]/ !archive export",
  config:"!config [point de configuration] [valeur]/$remove",
  counclistats:"!councilstats",
  council:"!council [nom] / !council remove",
  setweight:"!setweight [@membre/@rôle] [poids du vote]",
  help:"!help / !help [commande]"

  // add your commands here
}


export default class HelpCommand extends Command {
  constructor(client : CommandoClient) {
    super(client, {
      name: 'help',
      aliases: ['aide', 'h'],
      description: "Affiche la liste des commandes ou des informations sur une commande spécifique.",
      councilOnly: false, // help command available everywhere, not limited to council channel
      args: [
        {
          key: 'commandName',
          prompt: 'Sur quelle commande souhaitez-vous de l\'aide ?',
          type: 'string',
          default: '', 
        },
      ],
    });
  }

  public async execute(msg: CommandoMessage, args: { commandName: string }): Promise<any> {
    const { commandName } = args;

    // 1- Specific command (!help [command])
    if (commandName) {
      // check for command name or aliases
      const commands = this.client.registry.findCommands(commandName, true);
      if (commands.length === 0) {
        return msg.reply("Cette commande n'existe pas.");
      }
      
      const command = commands[0] as Command; 

      const embed = new MessageEmbed()
        .setColor('#3498db') //sidebar color
        .setTitle(`Aide pour la commande : \`!${command.name}\``)
        .setDescription(command.description);

      if (command.aliases && command.aliases.length > 0) {
        embed.addField('Alias', `\`${command.aliases.join('`, `')}\``);
      }

	  // displays who can use the command
      if (command.councilOnly) {
        embed.addField('Permission', 'Réservée au salon du conseil');
      }
      if (command.adminOnly) {
        embed.addField('Permission', 'Réservée aux administrateurs');
      }

    // displays the command's syntax
      const syntax = SYNTAXES[command.name]
      if (syntax) {
        embed.addField("Syntaxe", `\`${syntax}\``)
      }

      // Add detailed explanations for specific commands
      if (command.name === 'elections') {
        embed.addField('Arguments',
          '• **raison** : Motif de l\'élection (entre guillemets si espaces)\n' +
          '• **candDur** : Durée phase candidature en minutes\n' +
          '• **voteDur** : Durée phase vote en minutes\n' +
          '• **list** : Affiche toutes les élections en cours\n' +
          '• **status** : Affiche le statut de l\'élection active\n' +
          '• **kill** : Annule l\'élection active'
        );
        embed.addField('Exemples',
          '`!elections "Délégué de classe" 10 20` - Crée une élection\n' +
          '`!elections list` - Liste les élections\n' +
          '`!elections` - Affiche le statut'
        );
      } else if (command.name === 'motion') {
        embed.addField('Arguments',
          '• **texte** : Texte de la motion (entre guillemets si espaces)\n' +
          '• **list** : Affiche toutes les motion en cours\n' +
          '• **status [id]** : Affiche le statut d\'une motion\n' +
          '• **kill [id]** : Annule une motion (ID optionnel si une seule)'
        );
        embed.addField('Exemples',
          '`!motion "Proposition de changement"` - Crée une motion\n' +
          '`!motion list` - Liste les motion\n' +
          '`!motion status abc12345` - Statut de la motion abc12345'
        );
      } else if (command.name === 'candidat') {
        embed.addField('Arguments',
          '• **raison** : Motif de votre candidature\n' +
          '• **electionId** : ID de l\'élection (optionnel si une seule active)'
        );
        embed.addField('Exemples',
          '`!candidat "Je veux représenter la classe"` - Candidature simple\n' +
          '`!candidat "Ma candidature" abc12345` - Candidature pour élection spécifique'
        );
      } else if (command.name === 'vote') {
        embed.addField('Arguments',
          '• **vote** : pour/contre/blanc\n' +
          '• **raison** : Justification du vote (optionnel)\n' +
          '• **pollId** : ID de l\'élection/motion (optionnel si une seule active)'
        );
        embed.addField('Exemples',
          '`!vote pour "Je suis d\'accord"` - Vote pour avec raison\n' +
          '`!vote contre` - Vote contre simple\n' +
          '`!vote blanc "Je m\'abstiens" abc12345` - Vote blanc pour poll spécifique'
        );
      }

      return msg.embed(embed);
    } 
    // 2 - General case (!help)
    else {
      const embed = new MessageEmbed()
        .setColor('#2ecc71') // side bar color
        .setTitle('Panneau d\'aide de Démocratie')
        .setThumbnail(this.client.user?.displayAvatarURL() || '') // display updated avatar on the help pannel if null or undefined --> no picture
        .setDescription(`Voici la liste des commandes disponibles.
        Pour plus d'informations sur une commande, tapez \`!help <nom_de_la_commande>\`.`);
      

      // group commands by category
      const sortedGroups = Array.from(this.client.registry.groups.values())
          .sort((a, b) => a.name.localeCompare(b.name));

      for (const group of sortedGroups) {
        // ownerOnly commands arent displayed
        const commandList = group.commands
          .filter(cmd => !cmd.ownerOnly)
          .sort((a, b) => a.name.localeCompare(b.name))
          .map(cmd => `**${cmd.name}**: ${cmd.description}`); // display description next to command

        if (commandList.length > 0) {
          // Split commands into chunks to avoid 1024 character limit
          const chunks = [];
          let currentChunk = '';

          for (const command of commandList) {
            if (currentChunk.length + command.length + 1 > 1000) { // Leave some margin
              chunks.push(currentChunk);
              currentChunk = command;
            } else {
              currentChunk += (currentChunk ? '\n' : '') + command;
            }
          }
          if (currentChunk) chunks.push(currentChunk);

          // Add fields for each chunk
          chunks.forEach((chunk, index) => {
            const fieldName = index === 0 ? `Groupe : ${group.name}` : `${group.name} (suite)`;
            embed.addField(fieldName, chunk);
          });
        }
      }

      embed.setFooter('Vive la Démocratie');

      return msg.embed(embed);
    }
  }
}