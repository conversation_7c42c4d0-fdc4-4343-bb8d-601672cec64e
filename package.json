{"dependencies": {"ajv": "^6.12.3", "discord.js": "^12.5.1", "discord.js-commando": "git+https://github.com/discordjs/Commando.git#198d7604e3725ee88dceab9b5f296edb1b7580a5", "dotenv": "^5.0.1", "fp-ts": "^1.14.4", "io-ts": "^1.8.2", "minimist": "^1.2.6", "num2fraction": "^1.2.2", "on-change": "^0.1.0", "uuid": "^11.1.0"}, "devDependencies": {"@types/babel__traverse": "^7.14.2", "@types/jest": "^27.0.1", "@types/minimist": "^1.2.0", "@types/node": "^10.12.30", "@types/on-change": "^1.1.0", "@types/uuid": "^10.0.0", "@types/ws": "^7.2.3", "jest": "^27.1.0", "ts-node": "^10.9.2", "tsc-watch": "^5.0.3", "tslint": "^5.13.1", "tslint-config-standard": "^7.1.0", "typescript": "^3.9.7"}, "scripts": {"dev": "tsc-watch --onSuccess \"node dist/index.js\"", "test": "tsc && jest dist"}}