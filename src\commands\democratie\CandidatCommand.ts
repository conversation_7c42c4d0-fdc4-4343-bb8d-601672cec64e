import { Message } from "discord.js";
import { CommandoClient, CommandoMessage } from "discord.js-commando";
import Command from "../Command";

export default class CandidatCommand extends Command {
  constructor(client: CommandoClient) {
    super(client, {
      name: "candidat",
      description: "Se porter candidat aux élections (optionnel: spécifier l'ID de l'élection)",
      args: [
        {
          key: "reason",
          prompt: "Pourquoi te présenter ?",
          type: "string",
        },
        {
          key: "electionId",
          prompt: "ID de l'élection (optionnel)",
          type: "string",
          default: ""
        },
      ],
    });
  }

  async run(
    msg: CommandoMessage,
    args: any
  ): Promise<Message | Message[]> {
    const { reason, electionId } = args as { reason: string; electionId: string };
    const democratie = (this.client as any).democratie;
    const pollManager = democratie.pollManager;

    let targetId = electionId.trim();

    // If no ID provided, try to deduce from single active election
    if (!targetId) {
      const deducedId = pollManager.deduceElectionId();
      if (!deducedId) {
        return msg.reply("❌ Plusieurs élections actives. Spécifiez un ID avec: !candidat \"raison\" <electionId>");
      }
      targetId = deducedId;
    }

    // Find election by partial ID match
    const elections = pollManager.getActiveElections();
    const election = elections.find(({ id }: any) => id.startsWith(targetId))?.election;

    if (!election) {
      return msg.reply("❌ Élection introuvable avec cet ID.");
    }

    if (election.data.phase !== "Candidacy") {
      return msg.reply("❌ Cette élection n'est pas en phase de candidature.");
    }

    try {
      election.addCandidate(msg.author.id, msg.member!.displayName, reason);
      const shortId = targetId.substring(0, 8);
      return msg.reply(`✅ Candidature enregistrée pour l'élection ${shortId}.`);
    } catch (error) {
      return msg.reply("❌ Erreur lors de l'enregistrement de la candidature.");
    }
  }
}
