import Discord, { Message } from "discord.js"
import { CommandoClient, CommandoMessage } from "discord.js-commando"
import { PathReporter } from "io-ts/lib/PathReporter"
import Motion, { LegacyMotionVoteType, MotionResolution } from "../../Motion"
import { response, ResponseType } from "../../Util"
import Command from "../Command"

export default class MotionCommand extends Command {
  constructor(client: CommandoClient) {
    super(client, {
      name: "motion",
      aliases: ["propose", "proposal", "call"],
      description: "Créer une motion",

      allowWithConfigurableRoles: ["proposeRole"],
      adminsAlwaysAllowed: true,

      args: [
        {
          key: "text",
          prompt: "Le texte de la motion à proposer ou sous-commande (list, status, kill)",
          type: "string",
          default: "",
        },
      ],
    })
  }

  async execute(msg: CommandoMessage, args: any): Promise<Message | Message[]> {
    await msg.guild.members.fetch() // Privileged intents fix

    // Get motions from PollManager (backward compatibility)
    const democratie = (this.client as any).democratie
    const pollManager = democratie.pollManager
    const activeMotions = pollManager.getActiveMotions()

    // Check if this is a subcommand
    const subcommands = ["list", "status", "kill"]
    if (subcommands.includes(args.text.toLowerCase())) {
      return this.handleSubcommand(msg, pollManager, args.text.toLowerCase())
    }

    if (!args.text) {
      if (activeMotions.length > 0) {
        // Show the first active motion (legacy behavior)
        return activeMotions[0].motion.postMessage()
      } else {
        return msg.reply(
          "Aucune motion n'est active. Fait `!motion <texte>` pour créer une."
        )
      }
    }

    // Legacy kill handling (for backward compatibility)
    if (args.text === "kill") {
      if (activeMotions.length === 0) {
        return msg.reply("Il y a aucune motion active.")
      }

      const motion = activeMotions[0].motion
      if (
        motion.authorId === msg.author.id ||
        msg.member.hasPermission("MANAGE_GUILD") ||
        !!msg.member.roles.cache.find((role) => role.name === "Democratie Admin")
      ) {
        motion.resolve(MotionResolution.Killed)
        return motion.postMessage()
      } else {
        return msg.reply("Tu as pas la permission de tuer cette motion.")
      }
    }

    // Remove the old kill check since it's handled above

    if (this.council.getConfig("councilorMotionDisable")) {
      return msg.reply("La création de motions est désactivée dans ce conseil.")
    }

    const proposeRole = this.council.getConfig("proposeRole")
    if (proposeRole && !msg.member.roles.cache.has(proposeRole)) {
      return msg.reply("Tu as pas les permissions de proposer une motion.")
    }

    if (args.text.length > 2000) {
      return msg.reply(
        "Ta motion est trop longue. La taille limite est de 2000 caractères."
      )
    }

    if (this.council.isUserOnCooldown(msg.author.id)) {
      return msg.reply(
        `Tu dois attendre ${+(this.council.userCooldown / 3600000).toFixed(
          2
        )} heures entre chaque motions. (${+(
          this.council.getUserCooldown(msg.author.id) / 3600000
        ).toFixed(2)} heures restants)`
      )
    }

    let voteType = LegacyMotionVoteType.Majority

    const result = Motion.parseMotionOptions(args.text)

    if (result.isLeft()) {
      return msg.reply(
        response(ResponseType.Bad, PathReporter.report(result).join("\n"))
      )
    }

    const [text, options] = result.value

    if (
      options.majority &&
      options.majority < this.council.getConfig("majorityMinimum")
    ) {
      return msg.reply(
        response(
          ResponseType.Bad,
          //`The given majority type is disallowed by the ~majority.minimum~ configuration point. Please specify a higher majority.`
          `Le type de majorité spécifié n'est pas autorisé par le point de configuration ~majority.minimum~ . Veuillez spécifier un majorité plus haute.`
        )
      )
    }

    if (this.council.getConfig("userCooldownKill")) {
      this.council.setUserCooldown(msg.author.id, Date.now())
    }

    // Create motion using PollManager (multiple motions now supported)
    const { id, motion } = pollManager.createMotion(this.council, {
      text,
      authorId: msg.author.id,
      authorName: msg.member.displayName,
      createdAt: Date.now(),
      voteType,
      active: true,
      resolution: MotionResolution.Unresolved,
      didExpire: false,
      votes: [],
      options,
    })

    const shortId = id.substring(0, 8)
    await motion.postMessage(true)
    return msg.reply(`📋 Motion créée (ID: ${shortId}).`)
  }

  private async handleSubcommand(
    msg: CommandoMessage,
    pollManager: any,
    subcommand: string
  ): Promise<Message | Message[]> {
    switch (subcommand) {
      case "list":
        return this.handleList(msg, pollManager)
      case "status":
        return this.handleStatus(msg, pollManager, "")
      case "kill":
        return this.handleKill(msg, pollManager, "")
      default:
        return msg.reply("❌ Sous-commande invalide. Utilisez: list, status, kill")
    }
  }

  private async handleList(msg: CommandoMessage, pollManager: any): Promise<Message | Message[]> {
    const motions = pollManager.getActiveMotions()

    if (motions.length === 0) {
      return msg.reply("ℹ️ Aucune motion en cours.")
    }

    const embed = new Discord.MessageEmbed()
      .setColor("GREEN")
      .setTitle("📋 Motion en cours")

    for (const { id, motion } of motions) {
      const shortId = id.substring(0, 8)
      const votes = motion.getVotes()
      const status = `Pour: ${votes.yes} | Contre: ${votes.no} | Blanc: ${votes.abs}`

      embed.addField(
        `${shortId} - ${motion.text.substring(0, 50)}${motion.text.length > 50 ? '...' : ''}`,
        status,
        false
      )
    }

    return msg.embed(embed)
  }

  private async handleStatus(msg: CommandoMessage, pollManager: any, targetId: string): Promise<Message | Message[]> {
    // If no ID provided, try to deduce from single active motion
    if (!targetId) {
      const deducedId = pollManager.deduceMotionId()
      if (!deducedId) {
        const motions = pollManager.getActiveMotions()
        if (motions.length === 0) {
          return msg.reply("ℹ️ Aucune motion en cours.")
        }
        return msg.reply("❌ Plusieurs motion actives. Spécifiez un ID avec: !motion status <id>")
      }
      targetId = deducedId
    }

    // Find motion by partial ID match
    const motions = pollManager.getActiveMotions()
    const motion = motions.find(({ id }: any) => id.startsWith(targetId))?.motion

    if (!motion) {
      return msg.reply("❌ Motion introuvable avec cet ID.")
    }

    return motion.postMessage()
  }

  private async handleKill(msg: CommandoMessage, pollManager: any, targetId: string): Promise<Message | Message[]> {
    // If no ID provided, try to deduce from single active motion
    if (!targetId) {
      const deducedId = pollManager.deduceMotionId()
      if (!deducedId) {
        const motions = pollManager.getActiveMotions()
        if (motions.length === 0) {
          return msg.reply("ℹ️ Aucune motion en cours.")
        }
        return msg.reply("❌ Plusieurs motion actives. Spécifiez un ID avec: !motion kill <id>")
      }
      targetId = deducedId
    }

    // Find motion by partial ID match
    const motions = pollManager.getActiveMotions()
    const matchingMotion = motions.find(({ id }: any) => id.startsWith(targetId))

    if (!matchingMotion) {
      return msg.reply("❌ Motion introuvable avec cet ID.")
    }

    // Check permissions (similar to original MotionCommand)
    const motion = matchingMotion.motion
    if (
      motion.authorId !== msg.author.id &&
      !msg.member!.hasPermission("MANAGE_GUILD") &&
      !msg.member!.roles.cache.find((role) => role.name === "Democratie Admin")
    ) {
      return msg.reply("❌ Tu n'as pas la permission de tuer cette motion.")
    }

    const success = pollManager.killMotion(matchingMotion.id)
    if (success) {
      const shortId = matchingMotion.id.substring(0, 8)
      await motion.postMessage()
      return msg.say(`🛑 Motion ${shortId} annulée manuellement.`)
    } else {
      return msg.reply("❌ Impossible d'annuler cette motion.")
    }
  }
}
